#!/usr/bin/env python3
"""
Simple Browser-Use Job Application Demo
======================================

This demonstrates the browser-use library working with Ollama for job automation.
"""

import asyncio
import os
from dotenv import load_dotenv
from browser_use import Agent
from langchain_ollama import ChatOllama

# Load environment variables
load_dotenv()

async def simple_job_search_demo():
    """Simple demo of browser-use with job search"""
    print("🚀 Simple Browser-Use Job Search Demo")
    print("=" * 50)
    
    try:
        # Setup Ollama LLM
        llm = ChatOllama(model="qwen2.5:7b")
        print("✅ Ollama LLM initialized")
        
        # Create a simple agent
        agent = Agent(
            task="Go to LinkedIn.com and search for 'software engineer' jobs. Just browse and tell me what you see.",
            llm=llm
        )
        
        print("🔍 Starting job search demo...")
        result = await agent.run()
        
        print("\n📋 Demo Results:")
        print("=" * 30)
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

async def main():
    """Main demo function"""
    print("🌐 Browser-Use + Ollama Job Search Demo")
    print("This is a simple demonstration of browser-use working with Ollama")
    print()
    
    # Run the demo
    success = await simple_job_search_demo()
    
    if success:
        print("\n✅ Demo completed successfully!")
        print("The browser-use library is working with Ollama.")
        print("You can now build more complex job automation on top of this.")
    else:
        print("\n❌ Demo failed.")
        print("Please check that:")
        print("1. Ollama is running: ollama serve")
        print("2. qwen2.5:7b model is available: ollama list")
        print("3. Browser-use is properly installed")

if __name__ == "__main__":
    asyncio.run(main())
